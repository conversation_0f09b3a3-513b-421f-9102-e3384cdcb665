<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLQ TV - بين سبورت HD</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            padding: 1.5rem 0;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
        }

        .logo h1 {
            font-size: 3rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .logo span {
            font-size: 1.3rem;
            color: #ffffff;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 380px;
            gap: 2rem;
            padding: 2rem 0;
            min-height: calc(100vh - 140px);
        }

        .player-section {
            background: #000000;
            border-radius: 20px;
            overflow: hidden;
            border: 3px solid #ff6b35;
            box-shadow: 0 10px 40px rgba(255, 107, 53, 0.2);
            height: fit-content;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
            background: #000000;
        }

        #mainPlayer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            background: #000000;
        }

        .video-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #ffffff;
            z-index: 10;
            display: none;
        }

        .video-overlay.show {
            display: block;
        }

        .video-overlay i {
            font-size: 5rem;
            margin-bottom: 1rem;
            color: #ff6b35;
            opacity: 0.8;
        }

        .video-overlay h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #ff6b35;
        }

        .player-controls {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            padding: 1.5rem;
        }

        .current-channel {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .channel-info {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .channels-sidebar {
            background: #1a1a1a;
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid #333;
            height: fit-content;
            max-height: 85vh;
            overflow-y: auto;
        }

        .sidebar-header {
            color: #ff6b35;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 2rem;
            text-align: center;
            border-bottom: 3px solid #ff6b35;
            padding-bottom: 1rem;
        }

        .channel-category {
            margin-bottom: 2rem;
        }

        .category-title {
            color: #ffd700;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding: 0.8rem;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 12px;
            border-right: 5px solid #ffd700;
            text-align: center;
        }

        .channel-list {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }

        .channel-item {
            background: #2a2a2a;
            border: 2px solid #444;
            border-radius: 12px;
            padding: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .channel-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .channel-item:hover::before {
            left: 100%;
        }

        .channel-item:hover {
            background: #3a3a3a;
            border-color: #ff6b35;
            transform: translateX(-5px);
            box-shadow: 0 5px 20px rgba(255, 107, 53, 0.3);
        }

        .channel-item.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-color: #ff6b35;
            color: #ffffff;
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }

        .channel-item.loading {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #000000;
            border-color: #ffd700;
        }

        .channel-name {
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .channel-number {
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            padding: 0.3rem 0.6rem;
            border-radius: 50%;
            font-size: 0.9rem;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }

        .channel-description {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }

        .channel-status {
            position: absolute;
            top: 12px;
            left: 12px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .channel-status.loading {
            background: #ffd700;
            animation: pulse 1.5s infinite;
        }

        .channel-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 0.5rem;
        }

        .badge {
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .badge-live {
            background: #ff4444;
            color: #ffffff;
        }

        .badge-hd {
            background: #4CAF50;
            color: #ffffff;
        }

        .badge-premium {
            background: #ff6b35;
            color: #ffffff;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .message {
            position: fixed;
            top: 120px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: #ffffff;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 350px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .message.show {
            transform: translateX(0);
        }

        .message.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .message.error {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .message.info {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .message.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .loading-indicator {
            text-align: center;
            padding: 3rem;
            color: #ff6b35;
        }

        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #ff6b35;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .channels-sidebar {
                max-height: 500px;
                order: 2;
            }
            
            .player-section {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .logo h1 {
                font-size: 2.2rem;
            }
            
            .main-content {
                padding: 1rem 0;
            }
            
            .channels-sidebar {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>🏆 QLQ TV</h1>
                <span>بين سبورت HD - البث المباشر الحصري</span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <!-- Player Section -->
            <div class="player-section">
                <div class="video-container">
                    <video id="mainPlayer" controls crossorigin="anonymous" preload="metadata">
                        <source src="" type="application/x-mpegURL">
                        متصفحك لا يدعم تشغيل الفيديو
                    </video>
                    
                    <div class="video-overlay show" id="videoOverlay">
                        <i class="fas fa-play-circle"></i>
                        <h3>اختر قناة بين سبورت</h3>
                        <p>جميع قنوات بين سبورت HD متاحة الآن</p>
                    </div>
                </div>
                
                <div class="player-controls">
                    <div class="current-channel" id="currentChannelName">لم يتم اختيار قناة</div>
                    <div class="channel-info" id="currentChannelInfo">اختر قناة بين سبورت من القائمة لبدء المشاهدة</div>
                </div>
            </div>

            <!-- Channels Sidebar -->
            <div class="channels-sidebar">
                <div class="sidebar-header">
                    🔴 بين سبورت HD
                </div>
                
                <div class="loading-indicator" id="loadingIndicator">
                    <div class="spinner"></div>
                    <p>جاري تحميل قنوات بين سبورت...</p>
                </div>

                <div id="channelsContainer" style="display: none;">
                    <!-- سيتم تحميل القنوات هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- Message Container -->
    <div id="messageContainer"></div>

    <!-- HLS.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    
    <script>
        // قنوات بين سبورت HD من مصادر متقدمة
        const beinSportsChannels = {
            arabic: [
                {
                    id: 'bein_sports_1_hd',
                    name: 'beIN Sports 1 HD',
                    number: '1',
                    description: 'البث المباشر للمباريات الكبرى',
                    url: 'https://webudi.openhd.lol/lb/premium01/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium02/index.m3u8',
                        'https://d2e1asnsl7br7b.cloudfront.net/7782e205e72f43aeb4a48ec97f66ebbe/index_4.m3u8',
                        'https://premium-ott-live-cf.bein.com/out/u/bein_sports_1.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_2_hd',
                    name: 'beIN Sports 2 HD',
                    number: '2',
                    description: 'البث المباشر للمباريات',
                    url: 'https://webudi.openhd.lol/lb/premium03/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium04/index.m3u8',
                        'https://premium-ott-live-cf.bein.com/out/u/bein_sports_2.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_3_hd',
                    name: 'beIN Sports 3 HD',
                    number: '3',
                    description: 'البث المباشر للمباريات',
                    url: 'https://webudi.openhd.lol/lb/premium05/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium06/index.m3u8',
                        'https://premium-ott-live-cf.bein.com/out/u/bein_sports_3.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_4_hd',
                    name: 'beIN Sports 4 HD',
                    number: '4',
                    description: 'البث المباشر للمباريات',
                    url: 'https://webudi.openhd.lol/lb/premium07/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium08/index.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_5_hd',
                    name: 'beIN Sports 5 HD',
                    number: '5',
                    description: 'البث المباشر للمباريات',
                    url: 'https://webudi.openhd.lol/lb/premium09/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium10/index.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_6_hd',
                    name: 'beIN Sports 6 HD',
                    number: '6',
                    description: 'البث المباشر للمباريات',
                    url: 'https://webudi.openhd.lol/lb/premium11/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium12/index.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_7_hd',
                    name: 'beIN Sports 7 HD',
                    number: '7',
                    description: 'البث المباشر للمباريات',
                    url: 'https://webudi.openhd.lol/lb/premium13/index.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium14/index.m3u8'
                    ]
                }
            ],
            premium: [
                {
                    id: 'bein_sports_premium_1',
                    name: 'beIN Sports Premium 1',
                    number: 'P1',
                    description: 'بين سبورت بريميوم 1 - جودة فائقة',
                    url: 'https://bein-xtra-bein.amagi.tv/playlist1080p.m3u8',
                    backup_urls: [
                        'https://dai.google.com/linear/hls/event/3eKBdb6SRkqGhpzJRIqJAA/master.m3u8'
                    ]
                },
                {
                    id: 'bein_sports_premium_2',
                    name: 'beIN Sports Premium 2',
                    number: 'P2',
                    description: 'بين سبورت بريميوم 2 - جودة فائقة',
                    url: 'https://premium-ott-live-cf.bein.com/out/u/bein_premium_2.m3u8',
                    backup_urls: [
                        'https://webudi.openhd.lol/lb/premium15/index.m3u8'
                    ]
                }
            ]
        };

        let hls = null;
        let currentActiveChannel = null;

        // تحميل القنوات
        function loadChannels() {
            setTimeout(() => {
                renderChannels();
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('channelsContainer').style.display = 'block';
                showMessage('تم تحميل جميع قنوات بين سبورت HD بنجاح!', 'success');
            }, 2000);
        }

        // عرض القنوات
        function renderChannels() {
            const container = document.getElementById('channelsContainer');
            container.innerHTML = '';

            // عرض قنوات بين سبورت العربية
            if (beinSportsChannels.arabic.length > 0) {
                const arabicSection = createChannelSection('🏆 beIN Sports HD (1-7)', beinSportsChannels.arabic);
                container.appendChild(arabicSection);
            }

            // عرض قنوات بين سبورت بريميوم
            if (beinSportsChannels.premium.length > 0) {
                const premiumSection = createChannelSection('💎 beIN Sports Premium', beinSportsChannels.premium);
                container.appendChild(premiumSection);
            }
        }

        // إنشاء قسم قنوات
        function createChannelSection(title, channels) {
            const section = document.createElement('div');
            section.className = 'channel-category';

            const titleElement = document.createElement('div');
            titleElement.className = 'category-title';
            titleElement.textContent = title;

            const channelList = document.createElement('div');
            channelList.className = 'channel-list';

            channels.forEach(channel => {
                const channelElement = document.createElement('div');
                channelElement.className = 'channel-item';
                channelElement.innerHTML = `
                    <div class="channel-status"></div>
                    <div class="channel-name">
                        <span class="channel-number">${channel.number}</span>
                        ${channel.name}
                    </div>
                    <div class="channel-description">${channel.description}</div>
                    <div class="channel-badges">
                        <span class="badge badge-live">🔴 مباشر</span>
                        <span class="badge badge-hd">HD</span>
                        <span class="badge badge-premium">⭐ حصري</span>
                    </div>
                `;

                channelElement.addEventListener('click', () => {
                    playChannel(channel, channelElement);
                });

                channelList.appendChild(channelElement);
            });

            section.appendChild(titleElement);
            section.appendChild(channelList);

            return section;
        }

        // تشغيل قناة
        async function playChannel(channel, element) {
            // إزالة التحديد السابق
            if (currentActiveChannel) {
                currentActiveChannel.classList.remove('active', 'loading');
                currentActiveChannel.querySelector('.channel-status').classList.remove('loading');
            }

            // تحديد القناة الحالية
            currentActiveChannel = element;
            element.classList.add('loading');
            element.querySelector('.channel-status').classList.add('loading');

            // تحديث معلومات المشغل
            document.getElementById('currentChannelName').textContent = channel.name;
            document.getElementById('currentChannelInfo').textContent = `جاري تحميل ${channel.description}...`;

            showMessage(`جاري تحميل ${channel.name}...`, 'info');

            try {
                // تجربة الرابط الأساسي أولاً
                let success = await loadStream(channel.url);
                
                // إذا فشل، جرب الروابط الاحتياطية
                if (!success && channel.backup_urls) {
                    for (let i = 0; i < channel.backup_urls.length; i++) {
                        showMessage(`جاري تجربة الرابط البديل ${i + 1}...`, 'warning');
                        success = await loadStream(channel.backup_urls[i]);
                        if (success) break;
                    }
                }
                
                if (success) {
                    // نجح التحميل
                    element.classList.remove('loading');
                    element.classList.add('active');
                    element.querySelector('.channel-status').classList.remove('loading');
                    
                    document.getElementById('currentChannelInfo').textContent = channel.description;
                    document.getElementById('videoOverlay').classList.remove('show');
                    
                    showMessage(`✅ تم تحميل ${channel.name} بنجاح!`, 'success');
                } else {
                    // فشل التحميل
                    element.classList.remove('loading');
                    element.querySelector('.channel-status').classList.remove('loading');
                    
                    document.getElementById('currentChannelInfo').textContent = 'فشل في تحميل القناة - جرب قناة أخرى';
                    showMessage(`❌ فشل في تحميل ${channel.name} - جرب قناة أخرى`, 'error');
                }
            } catch (error) {
                console.error('Error loading channel:', error);
                element.classList.remove('loading');
                element.querySelector('.channel-status').classList.remove('loading');
                showMessage(`❌ خطأ في تحميل ${channel.name}`, 'error');
            }
        }

        // تحميل البث
        function loadStream(url) {
            return new Promise((resolve) => {
                const video = document.getElementById('mainPlayer');

                // تنظيف المشغل السابق
                if (hls) {
                    hls.destroy();
                    hls = null;
                }

                if (Hls.isSupported()) {
                    hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: false,
                        backBufferLength: 90,
                        maxBufferLength: 30,
                        debug: false,
                        capLevelToPlayerSize: true,
                        maxLoadingDelay: 4,
                        maxBufferHole: 0.5
                    });

                    let resolved = false;

                    hls.loadSource(url);
                    hls.attachMedia(video);

                    hls.on(Hls.Events.MANIFEST_PARSED, function() {
                        if (!resolved) {
                            resolved = true;
                            console.log('✅ Stream loaded successfully');
                            
                            // محاولة التشغيل التلقائي
                            video.play().catch(e => {
                                console.log('Autoplay prevented, user interaction required');
                                showMessage('انقر على زر التشغيل لبدء المشاهدة', 'warning');
                            });
                            
                            resolve(true);
                        }
                    });

                    hls.on(Hls.Events.ERROR, function(event, data) {
                        if (!resolved && data.fatal) {
                            resolved = true;
                            console.error('❌ HLS Error:', data.details);
                            resolve(false);
                        }
                    });

                    // timeout بعد 8 ثواني
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            console.log('⏰ Stream loading timeout');
                            resolve(false);
                        }
                    }, 8000);

                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    // دعم Safari الأصلي
                    video.src = url;
                    let resolved = false;

                    video.addEventListener('loadedmetadata', function() {
                        if (!resolved) {
                            resolved = true;
                            video.play().catch(e => {
                                showMessage('انقر على زر التشغيل لبدء المشاهدة', 'warning');
                            });
                            resolve(true);
                        }
                    });

                    video.addEventListener('error', function() {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    });

                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    }, 8000);
                } else {
                    resolve(false);
                }
            });
        }

        // إظهار رسالة
        function showMessage(text, type = 'info') {
            const container = document.getElementById('messageContainer');
            
            // إزالة الرسائل السابقة
            container.innerHTML = '';
            
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            
            container.appendChild(message);
            
            // إظهار الرسالة
            setTimeout(() => message.classList.add('show'), 100);
            
            // إخفاء الرسالة بعد 4 ثواني
            setTimeout(() => {
                message.classList.remove('show');
                setTimeout(() => {
                    if (container.contains(message)) {
                        container.removeChild(message);
                    }
                }, 300);
            }, 4000);
        }

        // تحميل عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
        });
    </script>
</body>
</html>
