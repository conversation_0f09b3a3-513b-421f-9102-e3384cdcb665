#!/usr/bin/env python3
"""
QLQ TV - beIN Connect Proxy Server
يتصل بموقع beIN Connect ويعرض القنوات في موقعنا
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import json
import re
import time
from urllib.error import URLError, HTTPError

class BeINConnectProxyHandler(http.server.SimpleHTTPRequestHandler):
    
    def do_GET(self):
        # إذا كان الطلب للـ proxy
        if self.path.startswith('/proxy/'):
            self.handle_proxy_request()
        # إذا كان الطلب لـ API beIN Connect
        elif self.path.startswith('/api/bein/'):
            self.handle_bein_api()
        # إذا كان الطلب لتحليل القنوات
        elif self.path.startswith('/channels/'):
            self.handle_channels_request()
        # طلبات الملفات العادية
        else:
            super().do_GET()
    
    def handle_proxy_request(self):
        """معالجة طلبات الـ proxy لـ beIN Connect"""
        try:
            # استخراج الرابط من المسار
            encoded_url = self.path[7:]  # إزالة /proxy/
            target_url = urllib.parse.unquote(encoded_url)
            
            print(f"Proxying beIN Connect request to: {target_url}")
            
            # إنشاء الطلب مع headers مناسبة لـ beIN Connect
            req = urllib.request.Request(target_url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            req.add_header('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8')
            req.add_header('Accept-Language', 'ar,en-US;q=0.7,en;q=0.3')
            req.add_header('Accept-Encoding', 'gzip, deflate')
            req.add_header('Referer', 'https://connect.bein.com/')
            req.add_header('Origin', 'https://connect.bein.com')
            
            # تنفيذ الطلب
            with urllib.request.urlopen(req, timeout=15) as response:
                content = response.read()
                content_type = response.headers.get('Content-Type', 'text/html')
                
                # معالجة المحتوى إذا كان HTML
                if 'text/html' in content_type:
                    content = self.process_bein_html(content.decode('utf-8', errors='ignore'))
                    content = content.encode('utf-8')
                
                # إرسال الاستجابة
                self.send_response(200)
                self.send_header('Content-Type', content_type)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', '*')
                self.send_header('X-Frame-Options', 'ALLOWALL')
                self.end_headers()
                self.wfile.write(content)
                
        except Exception as e:
            print(f"Proxy error: {e}")
            self.send_error(500, f"Proxy error: {str(e)}")
    
    def handle_bein_api(self):
        """معالجة طلبات API لاستخراج قائمة القنوات"""
        try:
            # محاكاة API للحصول على قائمة القنوات
            channels_data = self.get_bein_channels_data()
            
            # إرسال البيانات كـ JSON
            response_data = {
                'success': True,
                'channels': channels_data,
                'timestamp': int(time.time())
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            print(f"API error: {e}")
            self.send_error(500, f"API error: {str(e)}")
    
    def handle_channels_request(self):
        """معالجة طلبات الحصول على معلومات القنوات"""
        try:
            channel_id = self.path.split('/')[-1]
            channel_info = self.get_channel_info(channel_id)
            
            if channel_info:
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(channel_info, ensure_ascii=False).encode('utf-8'))
            else:
                self.send_error(404, "Channel not found")
                
        except Exception as e:
            print(f"Channels error: {e}")
            self.send_error(500, f"Channels error: {str(e)}")
    
    def process_bein_html(self, html_content):
        """معالجة محتوى HTML من beIN Connect"""
        try:
            # إزالة قيود iframe
            html_content = html_content.replace('X-Frame-Options', 'X-Frame-Options-Disabled')
            html_content = html_content.replace('frame-ancestors', 'frame-ancestors-disabled')
            
            # إضافة CSS لإخفاء العناصر غير المرغوب فيها
            css_injection = """
            <style>
                /* إخفاء الإعلانات والعناصر غير المرغوب فيها */
                .advertisement, .ads, .banner, .popup { display: none !important; }
                .header, .footer, .navigation { display: none !important; }
                .video-player { width: 100% !important; height: 100% !important; }
                body { margin: 0 !important; padding: 0 !important; }
            </style>
            """
            
            # إدراج CSS في head
            html_content = html_content.replace('</head>', css_injection + '</head>')
            
            # إضافة JavaScript لتحسين التشغيل
            js_injection = """
            <script>
                // إزالة قيود iframe
                if (window.top !== window.self) {
                    document.domain = document.domain;
                }
                
                // تحسين تشغيل الفيديو
                document.addEventListener('DOMContentLoaded', function() {
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        video.setAttribute('crossorigin', 'anonymous');
                        video.setAttribute('playsinline', 'true');
                    });
                });
            </script>
            """
            
            html_content = html_content.replace('</body>', js_injection + '</body>')
            
            return html_content
            
        except Exception as e:
            print(f"HTML processing error: {e}")
            return html_content
    
    def get_bein_channels_data(self):
        """الحصول على بيانات القنوات من beIN Connect"""
        return {
            'sports': [
                {
                    'id': 'bein-sports-1',
                    'name': 'beIN Sports 1 HD',
                    'description': 'البث المباشر للمباريات الكبرى',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-2',
                    'name': 'beIN Sports 2 HD',
                    'description': 'البث المباشر للمباريات',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-3',
                    'name': 'beIN Sports 3 HD',
                    'description': 'البث المباشر للمباريات',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-4',
                    'name': 'beIN Sports 4 HD',
                    'description': 'البث المباشر للمباريات',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-5',
                    'name': 'beIN Sports 5 HD',
                    'description': 'البث المباشر للمباريات',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-6',
                    'name': 'beIN Sports 6 HD',
                    'description': 'البث المباشر للمباريات',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-7',
                    'name': 'beIN Sports 7 HD',
                    'description': 'البث المباشر للمباريات',
                    'category': 'sports',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-en-1',
                    'name': 'beIN Sports English 1',
                    'description': 'Live Sports in English',
                    'category': 'sports',
                    'language': 'en',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-en-2',
                    'name': 'beIN Sports English 2',
                    'description': 'Live Sports in English',
                    'category': 'sports',
                    'language': 'en',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-sports-en-3',
                    'name': 'beIN Sports English 3',
                    'description': 'Live Sports in English',
                    'category': 'sports',
                    'language': 'en',
                    'quality': 'HD',
                    'status': 'live'
                }
            ],
            'entertainment': [
                {
                    'id': 'bein-movies-1',
                    'name': 'beIN Movies 1',
                    'description': 'أفلام ومسلسلات',
                    'category': 'movies',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-movies-2',
                    'name': 'beIN Movies 2',
                    'description': 'أفلام ومسلسلات',
                    'category': 'movies',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                },
                {
                    'id': 'bein-series',
                    'name': 'beIN Series',
                    'description': 'مسلسلات وبرامج',
                    'category': 'series',
                    'language': 'ar',
                    'quality': 'HD',
                    'status': 'live'
                }
            ]
        }
    
    def get_channel_info(self, channel_id):
        """الحصول على معلومات قناة محددة"""
        all_channels = self.get_bein_channels_data()
        
        # البحث في جميع الفئات
        for category in all_channels.values():
            for channel in category:
                if channel['id'] == channel_id:
                    # إضافة معلومات إضافية
                    channel['stream_url'] = f'https://connect.bein.com/live/{channel_id}'
                    channel['embed_url'] = f'https://connect.bein.com/embed/{channel_id}'
                    channel['proxy_url'] = f'/proxy/https://connect.bein.com/live/{channel_id}'
                    return channel
        
        return None
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()

def run_server(port=8080):
    """تشغيل الخادم"""
    try:
        with socketserver.TCPServer(("", port), BeINConnectProxyHandler) as httpd:
            print("=" * 60)
            print("    QLQ TV - beIN Connect Proxy Server")
            print("=" * 60)
            print(f"Server running on port {port}")
            print(f"Open your browser and go to: http://localhost:{port}/bein_connect_integration.html")
            print("Features:")
            print("  - Direct integration with beIN Connect")
            print("  - All beIN Sports channels")
            print("  - Movies and Series channels")
            print("  - CORS bypass for seamless streaming")
            print("Press Ctrl+C to stop the server")
            print("=" * 60)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")

if __name__ == "__main__":
    run_server()
