<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLQ TV - بين سبورت الحقيقية النهائية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            padding: 1rem 0;
            text-align: center;
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .logo span {
            font-size: 1.2rem;
            color: #ffffff;
        }

        .player-section {
            padding: 2rem 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
        }

        .player-container {
            background: #000000;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 2rem;
            border: 2px solid #ff6b35;
        }

        #videoPlayer {
            width: 100%;
            height: 500px;
            background: #000000;
        }

        .player-info {
            padding: 1.5rem;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            text-align: center;
        }

        .channels-section {
            padding: 2rem 0;
        }

        .channels-section h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #ff6b35;
        }

        .channels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .channel-card {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #333;
        }

        .channel-card:hover {
            transform: translateY(-5px);
            border-color: #ff6b35;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
        }

        .channel-card.playing {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #1a2a1a 0%, #2a3a2a 100%);
        }

        .channel-card.testing {
            border-color: #ffd700;
            background: linear-gradient(135deg, #2a2a1a 0%, #3a3a2a 100%);
        }

        .channel-logo {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #ffffff;
            font-weight: bold;
            position: relative;
        }

        .bein-number {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffffff;
            color: #ff6b35;
            padding: 8px 12px;
            border-radius: 50%;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #666;
        }

        .status-indicator.testing {
            background: #ffd700;
            animation: pulse 1s infinite;
        }

        .status-indicator.working {
            background: #4CAF50;
        }

        .status-indicator.failed {
            background: #ff4444;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .channel-info {
            padding: 1rem;
        }

        .channel-info h4 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #ff6b35;
        }

        .channel-info p {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .channel-urls {
            margin: 0.5rem 0;
            font-size: 0.8rem;
            color: #888;
        }

        .url-status {
            display: inline-block;
            margin: 2px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .url-status.testing {
            background: #ffd700;
            color: #000;
        }

        .url-status.working {
            background: #4CAF50;
            color: #fff;
        }

        .url-status.failed {
            background: #ff4444;
            color: #fff;
        }

        .channel-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .status-live {
            background: #ff4444;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .quality-badge {
            background: #4CAF50;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .bein-badge {
            background: #ff6b35;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            display: none;
        }

        .error-message {
            background: #ff4444;
            color: #ffffff;
        }

        .success-message {
            background: #4CAF50;
            color: #ffffff;
        }

        .warning-message {
            background: #ff9800;
            color: #ffffff;
        }

        .info-message {
            background: #2196F3;
            color: #ffffff;
        }

        .test-all-btn {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: #ffffff;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 2rem auto;
            display: block;
            transition: all 0.3s ease;
        }

        .test-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }

        @media (max-width: 768px) {
            #videoPlayer {
                height: 250px;
            }

            .channels-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>🏆 QLQ TV - بين سبورت</h1>
                <span>الحل النهائي لقنوات بين سبورت الحقيقية</span>
            </div>
        </div>
    </header>

    <!-- Video Player Section -->
    <section class="player-section">
        <div class="container">
            <div class="player-container">
                <video id="videoPlayer" controls crossorigin="anonymous">
                    متصفحك لا يدعم تشغيل الفيديو
                </video>
                <div class="player-info">
                    <h3 id="currentChannel">اختر قناة بين سبورت للمشاهدة</h3>
                    <p id="channelDescription">روابط متعددة من مصادر مختلفة لضمان العمل</p>
                </div>
            </div>

            <button class="test-all-btn" onclick="testAllChannels()">
                🔍 اختبار جميع القنوات تلقائياً
            </button>

            <div class="message error-message" id="errorMessage"></div>
            <div class="message success-message" id="successMessage"></div>
            <div class="message warning-message" id="warningMessage"></div>
            <div class="message info-message" id="infoMessage"></div>
        </div>
    </section>

    <!-- Channels Section -->
    <section class="channels-section">
        <div class="container">
            <h2>🏆 قنوات بين سبورت (1-7)</h2>

            <div class="channels-grid" id="channelsGrid">
                <!-- سيتم تحميل القنوات هنا -->
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        // قنوات بين سبورت مع روابط متعددة من مصادر مختلفة
        const beinSportsChannels = [
            {
                id: 'beinsports1',
                name: 'بي إن سبورت 1 HD',
                number: '1',
                urls: [
                    // مصادر مختلفة لبين سبورت 1
                    'https://webudi.openhd.lol/lb/premium01/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium02/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports1_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/1/master.m3u8',
                    'https://bein1-live-tv.herokuapp.com/bein1.m3u8',
                    'https://live.bein.net/bein1/playlist.m3u8',
                    'https://stream.beinsports.com/bein1/index.m3u8',
                    'https://live-hls-web-aje.getaj.net/AJE/bein1.m3u8',
                    'https://ostoraapp.firebaseio.com/streams/beinsports1.m3u8',
                    'https://d2e1asnsl7br7b.cloudfront.net/7782e205e72f43aeb4a48ec97f66ebbe/index_4.m3u8'
                ],
                description: 'بي إن سبورت 1 - البث المباشر للمباريات',
                urlStatuses: []
            },
            {
                id: 'beinsports2',
                name: 'بي إن سبورت 2 HD',
                number: '2',
                urls: [
                    'https://webudi.openhd.lol/lb/premium03/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium04/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports2_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/2/master.m3u8',
                    'https://bein2-live-tv.herokuapp.com/bein2.m3u8',
                    'https://live.bein.net/bein2/playlist.m3u8'
                ],
                description: 'بي إن سبورت 2 - البث المباشر للمباريات',
                urlStatuses: []
            },
            {
                id: 'beinsports3',
                name: 'بي إن سبورت 3 HD',
                number: '3',
                urls: [
                    'https://webudi.openhd.lol/lb/premium05/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium06/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports3_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/3/master.m3u8',
                    'https://bein3-live-tv.herokuapp.com/bein3.m3u8'
                ],
                description: 'بي إن سبورت 3 - البث المباشر للمباريات',
                urlStatuses: []
            },
            {
                id: 'beinsports4',
                name: 'بي إن سبورت 4 HD',
                number: '4',
                urls: [
                    'https://webudi.openhd.lol/lb/premium07/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium08/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports4_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/4/master.m3u8'
                ],
                description: 'بي إن سبورت 4 - البث المباشر للمباريات',
                urlStatuses: []
            },
            {
                id: 'beinsports5',
                name: 'بي إن سبورت 5 HD',
                number: '5',
                urls: [
                    'https://webudi.openhd.lol/lb/premium09/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium10/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports5_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/5/master.m3u8'
                ],
                description: 'بي إن سبورت 5 - البث المباشر للمباريات',
                urlStatuses: []
            },
            {
                id: 'beinsports6',
                name: 'بي إن سبورت 6 HD',
                number: '6',
                urls: [
                    'https://webudi.openhd.lol/lb/premium11/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium12/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports6_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/6/master.m3u8'
                ],
                description: 'بي إن سبورت 6 - البث المباشر للمباريات',
                urlStatuses: []
            },
            {
                id: 'beinsports7',
                name: 'بي إن سبورت 7 HD',
                number: '7',
                urls: [
                    'https://webudi.openhd.lol/lb/premium13/index.m3u8',
                    'https://webudi.openhd.lol/lb/premium14/index.m3u8',
                    'https://d1211whpimeups.cloudfront.net/smil:ott/beinsports7_ar.smil/playlist.m3u8',
                    'https://siloh.pluto.tv/lilo/production/bein/7/master.m3u8'
                ],
                description: 'بي إن سبورت 7 - البث المباشر للمباريات',
                urlStatuses: []
            }
        ];

        let hls;
        let currentPlayingChannel = null;
        let testingInProgress = false;

        // تحميل القنوات
        function loadChannels() {
            renderChannels();
            setupHLS();
            showInfo('تم تحميل 7 قنوات بين سبورت مع روابط متعددة - اضغط "اختبار جميع القنوات" للبحث عن الروابط التي تعمل');
        }

        // عرض القنوات
        function renderChannels() {
            const channelsGrid = document.getElementById('channelsGrid');
            channelsGrid.innerHTML = '';

            beinSportsChannels.forEach(channel => {
                const channelCard = document.createElement('div');
                channelCard.className = 'channel-card';
                if (currentPlayingChannel === channel.id) {
                    channelCard.classList.add('playing');
                }

                // إنشاء مؤشرات حالة الروابط
                let urlStatusesHtml = '';
                if (channel.urlStatuses.length > 0) {
                    urlStatusesHtml = '<div class="channel-urls">الروابط: ';
                    channel.urlStatuses.forEach((status, index) => {
                        urlStatusesHtml += `<span class="url-status ${status}">رابط ${index + 1}</span>`;
                    });
                    urlStatusesHtml += '</div>';
                }

                channelCard.innerHTML = `
                    <div class="channel-logo">
                        ${channel.name}
                        <div class="bein-number">${channel.number}</div>
                        <div class="status-indicator" id="status-${channel.id}"></div>
                    </div>
                    <div class="channel-info">
                        <h4>${channel.name}</h4>
                        <p>${channel.description}</p>
                        ${urlStatusesHtml}
                        <div class="channel-badges">
                            <span class="status-live">مباشر</span>
                            <span class="quality-badge">HD</span>
                            <span class="bein-badge">🏆 بين سبورت</span>
                        </div>
                    </div>
                `;

                channelCard.addEventListener('click', () => {
                    if (!testingInProgress) {
                        playChannel(channel);
                    }
                });

                channelsGrid.appendChild(channelCard);
            });
        }

        // تشغيل قناة
        async function playChannel(channel) {
            hideMessages();
            showInfo('جاري البحث عن رابط يعمل لقناة ' + channel.name + '...');

            document.getElementById('currentChannel').textContent = channel.name;
            document.getElementById('channelDescription').textContent = channel.description;

            console.log('Playing channel:', channel.name);

            // تحديث حالة القناة
            updateChannelStatus(channel.id, 'testing');

            // تجربة الروابط واحد تلو الآخر
            for (let i = 0; i < channel.urls.length; i++) {
                const url = channel.urls[i];
                console.log(`Testing URL ${i + 1} for ${channel.name}:`, url);

                try {
                    showInfo(`جاري تجربة الرابط ${i + 1} من ${channel.urls.length} لقناة ${channel.name}...`);

                    // تحديث حالة الرابط
                    channel.urlStatuses[i] = 'testing';
                    renderChannels();

                    const success = await tryPlayUrl(url, channel, 6000); // 6 ثواني timeout

                    if (success) {
                        channel.urlStatuses[i] = 'working';
                        showSuccess(`✅ تم العثور على رابط يعمل لقناة ${channel.name}! (الرابط ${i + 1})`);
                        updateChannelStatus(channel.id, 'working');
                        currentPlayingChannel = channel.id;
                        renderChannels();
                        return;
                    } else {
                        channel.urlStatuses[i] = 'failed';
                    }
                } catch (error) {
                    console.log(`URL ${i + 1} failed for ${channel.name}:`, error);
                    channel.urlStatuses[i] = 'failed';
                }

                renderChannels();
            }

            // إذا فشلت جميع الروابط
            updateChannelStatus(channel.id, 'failed');
            showError(`❌ فشل في العثور على رابط يعمل لقناة ${channel.name} من جميع الروابط المتاحة (${channel.urls.length} روابط)`);
            renderChannels();
        }

        // تجربة تشغيل رابط
        function tryPlayUrl(url, channel, timeout = 6000) {
            return new Promise((resolve) => {
                const videoPlayer = document.getElementById('videoPlayer');

                if (hls) {
                    hls.destroy();
                }

                if (Hls.isSupported()) {
                    hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: false,
                        backBufferLength: 90,
                        maxBufferLength: 30,
                        debug: false
                    });

                    let resolved = false;

                    hls.loadSource(url);
                    hls.attachMedia(videoPlayer);

                    hls.on(Hls.Events.MANIFEST_PARSED, function() {
                        if (!resolved) {
                            resolved = true;
                            console.log('✅ Success for:', channel.name);
                            videoPlayer.play().catch(e => {
                                console.log('Autoplay prevented:', e);
                                showWarning('انقر على زر التشغيل لبدء المشاهدة');
                            });
                            resolve(true);
                        }
                    });

                    hls.on(Hls.Events.ERROR, function(event, data) {
                        if (!resolved && data.fatal) {
                            resolved = true;
                            console.error('❌ Failed for:', channel.name, data.details);
                            resolve(false);
                        }
                    });

                    // timeout
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            console.log('⏰ Timeout for:', channel.name);
                            resolve(false);
                        }
                    }, timeout);

                } else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
                    videoPlayer.src = url;
                    let resolved = false;

                    videoPlayer.addEventListener('loadedmetadata', function() {
                        if (!resolved) {
                            resolved = true;
                            resolve(true);
                        }
                    });

                    videoPlayer.addEventListener('error', function(e) {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    });

                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    }, timeout);
                } else {
                    resolve(false);
                }
            });
        }

        // تحديث حالة القناة
        function updateChannelStatus(channelId, status) {
            const statusIndicator = document.getElementById(`status-${channelId}`);
            if (statusIndicator) {
                statusIndicator.className = `status-indicator ${status}`;
            }

            const channelCard = document.querySelector(`[data-channel="${channelId}"]`);
            if (channelCard) {
                channelCard.className = `channel-card ${status}`;
            }
        }

        // اختبار جميع القنوات
        async function testAllChannels() {
            if (testingInProgress) return;

            testingInProgress = true;
            hideMessages();
            showInfo('🔍 جاري اختبار جميع قنوات بين سبورت... هذا قد يستغرق بضع دقائق');

            // إعادة تعيين حالات الروابط
            beinSportsChannels.forEach(channel => {
                channel.urlStatuses = [];
                updateChannelStatus(channel.id, '');
            });
            renderChannels();

            let workingChannels = 0;

            for (const channel of beinSportsChannels) {
                showInfo(`🔍 اختبار ${channel.name}...`);
                updateChannelStatus(channel.id, 'testing');

                let channelWorking = false;

                for (let i = 0; i < channel.urls.length; i++) {
                    const url = channel.urls[i];
                    console.log(`Testing ${channel.name} URL ${i + 1}:`, url);

                    channel.urlStatuses[i] = 'testing';
                    renderChannels();

                    try {
                        const success = await tryPlayUrl(url, channel, 4000); // 4 ثواني للاختبار السريع

                        if (success) {
                            channel.urlStatuses[i] = 'working';
                            channelWorking = true;
                            workingChannels++;
                            updateChannelStatus(channel.id, 'working');
                            console.log(`✅ ${channel.name} working with URL ${i + 1}`);
                            break; // توقف عند أول رابط يعمل
                        } else {
                            channel.urlStatuses[i] = 'failed';
                        }
                    } catch (error) {
                        channel.urlStatuses[i] = 'failed';
                    }

                    renderChannels();
                }

                if (!channelWorking) {
                    updateChannelStatus(channel.id, 'failed');
                }

                renderChannels();
            }

            testingInProgress = false;

            if (workingChannels > 0) {
                showSuccess(`✅ تم الانتهاء من الاختبار! ${workingChannels} من أصل ${beinSportsChannels.length} قنوات تعمل`);
            } else {
                showError(`❌ لم يتم العثور على أي قناة تعمل حالياً. جرب مرة أخرى لاحقاً`);
            }
        }

        // إعداد HLS
        function setupHLS() {
            if (!Hls.isSupported()) {
                console.log('HLS not supported, checking native support...');
            }
        }

        // إظهار رسائل
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 8000);
        }

        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 5000);
        }

        function showWarning(message) {
            hideMessages();
            const warningDiv = document.getElementById('warningMessage');
            warningDiv.textContent = message;
            warningDiv.style.display = 'block';
            setTimeout(() => warningDiv.style.display = 'none', 4000);
        }

        function showInfo(message) {
            hideMessages();
            const infoDiv = document.getElementById('infoMessage');
            infoDiv.textContent = message;
            infoDiv.style.display = 'block';
            setTimeout(() => infoDiv.style.display = 'none', 3000);
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('warningMessage').style.display = 'none';
            document.getElementById('infoMessage').style.display = 'none';
        }

        // تحميل عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
        });
    </script>
</body>
</html>
