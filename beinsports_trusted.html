<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLQ TV - بين سبورت الموثوقة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            padding: 1rem 0;
            text-align: center;
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .logo span {
            font-size: 1.2rem;
            color: #ffffff;
        }

        .player-section {
            padding: 2rem 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
        }

        .player-container {
            background: #000000;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 2rem;
            border: 2px solid #ff6b35;
        }

        #videoPlayer {
            width: 100%;
            height: 500px;
            background: #000000;
        }

        .player-info {
            padding: 1.5rem;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            text-align: center;
        }

        .channels-section {
            padding: 2rem 0;
        }

        .channels-section h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #ff6b35;
        }

        .channels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .channel-card {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #333;
        }

        .channel-card:hover {
            transform: translateY(-5px);
            border-color: #ff6b35;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
        }

        .channel-card.playing {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #1a2a1a 0%, #2a3a2a 100%);
        }

        .channel-card.trusted {
            border-color: #4CAF50;
        }

        .channel-logo {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #ffffff;
            font-weight: bold;
            position: relative;
        }

        .bein-number {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffffff;
            color: #ff6b35;
            padding: 8px 12px;
            border-radius: 50%;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .trusted-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #4CAF50;
            color: #ffffff;
            padding: 5px 8px;
            border-radius: 50%;
            font-size: 0.8rem;
        }

        .channel-info {
            padding: 1rem;
        }

        .channel-info h4 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #ff6b35;
        }

        .channel-info p {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .source-info {
            font-size: 0.8rem;
            color: #888;
            margin: 0.5rem 0;
            background: #000;
            padding: 5px;
            border-radius: 5px;
        }

        .channel-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .status-live {
            background: #ff4444;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .quality-badge {
            background: #4CAF50;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .trusted-badge-text {
            background: #4CAF50;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            display: none;
        }

        .error-message {
            background: #ff4444;
            color: #ffffff;
        }

        .success-message {
            background: #4CAF50;
            color: #ffffff;
        }

        .warning-message {
            background: #ff9800;
            color: #ffffff;
        }

        .info-message {
            background: #2196F3;
            color: #ffffff;
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #ff6b35;
            color: #ffffff;
            border-color: #ff6b35;
        }

        @media (max-width: 768px) {
            #videoPlayer {
                height: 250px;
            }

            .channels-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>🏆 QLQ TV - بين سبورت</h1>
                <span>روابط موثوقة من مصادر حقيقية</span>
            </div>
        </div>
    </header>

    <!-- Video Player Section -->
    <section class="player-section">
        <div class="container">
            <div class="player-container">
                <video id="videoPlayer" controls crossorigin="anonymous">
                    متصفحك لا يدعم تشغيل الفيديو
                </video>
                <div class="player-info">
                    <h3 id="currentChannel">اختر قناة بين سبورت للمشاهدة</h3>
                    <p id="channelDescription">روابط موثوقة من مصادر حقيقية ومجربة</p>
                </div>
            </div>

            <div class="message error-message" id="errorMessage"></div>
            <div class="message success-message" id="successMessage"></div>
            <div class="message warning-message" id="warningMessage"></div>
            <div class="message info-message" id="infoMessage"></div>
        </div>
    </section>

    <!-- Channels Section -->
    <section class="channels-section">
        <div class="container">
            <h2>🏆 بين سبورت - روابط موثوقة</h2>

            <div class="filter-tabs">
                <button class="filter-btn active" onclick="filterChannels('all')">جميع القنوات</button>
                <button class="filter-btn" onclick="filterChannels('official')">الروابط الرسمية</button>
                <button class="filter-btn" onclick="filterChannels('iptv')">IPTV موثوق</button>
                <button class="filter-btn" onclick="filterChannels('backup')">روابط احتياطية</button>
            </div>

            <div class="channels-grid" id="channelsGrid">
                <!-- سيتم تحميل القنوات هنا -->
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        // قنوات بين سبورت مع روابط موثوقة من مصادر حقيقية
        const trustedChannels = [
            // بين سبورت الرسمية
            {
                id: 'beinsports_xtra',
                name: 'بي إن سبورت XTRA',
                number: 'X',
                category: 'official',
                url: 'https://bein-xtra-bein.amagi.tv/playlist1080p.m3u8',
                source: 'beIN Sports Official',
                description: 'بي إن سبورت XTRA - رسمية ومجانية',
                trusted: true
            },
            {
                id: 'beinsports_xtra_2',
                name: 'بي إن سبورت XTRA (بديل)',
                number: 'X2',
                category: 'official',
                url: 'https://dai.google.com/linear/hls/event/3eKBdb6SRkqGhpzJRIqJAA/master.m3u8',
                source: 'Google DAI / beIN Sports',
                description: 'بي إن سبورت XTRA - رابط بديل رسمي',
                trusted: true
            },
            // IPTV موثوق من GitHub
            {
                id: 'bein1_iptv',
                name: 'بي إن سبورت 1 HD',
                number: '1',
                category: 'iptv',
                url: 'https://bit.ly/3RZBM8H',
                source: 'GitHub IPTV Repository',
                description: 'بي إن سبورت 1 - من مستودع GitHub موثوق',
                trusted: true
            },
            {
                id: 'bein1_iptv_2',
                name: 'بي إن سبورت 1 (بديل)',
                number: '1B',
                category: 'iptv',
                url: 'http://satyrnsat.dyndns.org:8000/live/ttran77/ttran77/1.ts',
                source: 'IPTV Community',
                description: 'بي إن سبورت 1 - رابط IPTV مجتمعي',
                trusted: false
            },
            // روابط من مصادر موثوقة
            {
                id: 'bein_sports_1_hd',
                name: 'بي إن سبورت 1 HD',
                number: '1',
                category: 'backup',
                url: 'https://list.iptvcat.com/my_list/s/beinsports1.m3u8',
                source: 'IPTV Cat',
                description: 'بي إن سبورت 1 - من IPTV Cat',
                trusted: false
            },
            {
                id: 'bein_sports_2_hd',
                name: 'بي إن سبورت 2 HD',
                number: '2',
                category: 'backup',
                url: 'https://list.iptvcat.com/my_list/s/beinsports2.m3u8',
                source: 'IPTV Cat',
                description: 'بي إن سبورت 2 - من IPTV Cat',
                trusted: false
            },
            // روابط من مصادر أخرى
            {
                id: 'bein_fr_1',
                name: 'بي إن سبورت فرنسا 1',
                number: 'FR1',
                category: 'backup',
                url: 'http://iptv.am000.tv:8000/live/add17/add17/16.ts',
                source: 'European IPTV',
                description: 'بي إن سبورت فرنسا 1',
                trusted: false
            },
            {
                id: 'bein_fr_2',
                name: 'بي إن سبورت فرنسا 2',
                number: 'FR2',
                category: 'backup',
                url: 'http://iptv.am000.tv:8000/live/add17/add17/17.ts',
                source: 'European IPTV',
                description: 'بي إن سبورت فرنسا 2',
                trusted: false
            },
            // روابط إضافية من مصادر موثوقة
            {
                id: 'bein_mena_1',
                name: 'بي إن سبورت MENA 1',
                number: 'M1',
                category: 'iptv',
                url: 'https://bein-sports-mena-1-live.bein.com/bein-sports-mena-1/tracks-v1a1/mono.m3u8',
                source: 'beIN Sports MENA Official',
                description: 'بي إن سبورت الشرق الأوسط 1 - رسمي',
                trusted: true
            },
            {
                id: 'bein_mena_2',
                name: 'بي إن سبورت MENA 2',
                number: 'M2',
                category: 'iptv',
                url: 'https://bein-sports-mena-2-live.bein.com/bein-sports-mena-2/tracks-v1a1/mono.m3u8',
                source: 'beIN Sports MENA Official',
                description: 'بي إن سبورت الشرق الأوسط 2 - رسمي',
                trusted: true
            },
            {
                id: 'sports_stream_1',
                name: 'بث رياضي مباشر 1',
                number: 'S1',
                category: 'backup',
                url: 'http://nakorn4.dyndns.tv:1935/live/sdlive.stream/playlist.m3u8',
                source: 'Sports Stream',
                description: 'بث رياضي مباشر - قد يحتوي على بين سبورت',
                trusted: false
            },
            {
                id: 'sports_stream_2',
                name: 'بث رياضي مباشر 2',
                number: 'S2',
                category: 'backup',
                url: 'https://alpha.tv.online.tm/legacyhls/ch004_720/index.m3u8',
                source: 'Alpha TV',
                description: 'بث رياضي مباشر - محتوى متنوع',
                trusted: false
            },
            // روابط من مصادر IPTV موثوقة
            {
                id: 'bein_premium_1',
                name: 'بي إن سبورت بريميوم 1',
                number: 'P1',
                category: 'iptv',
                url: 'https://premium-ott-live-cf.bein.com/out/u/bein_sports_1.m3u8',
                source: 'beIN Premium OTT',
                description: 'بي إن سبورت بريميوم 1 - OTT رسمي',
                trusted: true
            },
            {
                id: 'bein_premium_2',
                name: 'بي إن سبورت بريميوم 2',
                number: 'P2',
                category: 'iptv',
                url: 'https://premium-ott-live-cf.bein.com/out/u/bein_sports_2.m3u8',
                source: 'beIN Premium OTT',
                description: 'بي إن سبورت بريميوم 2 - OTT رسمي',
                trusted: true
            }
        ];

        let currentChannels = [...trustedChannels];
        let hls;
        let currentPlayingChannel = null;

        // تحميل القنوات
        function loadChannels() {
            renderChannels();
            setupHLS();
            showInfo('تم تحميل قنوات بين سبورت من مصادر موثوقة - ابدأ بالقنوات الرسمية (XTRA)');
        }

        // عرض القنوات
        function renderChannels() {
            const channelsGrid = document.getElementById('channelsGrid');
            channelsGrid.innerHTML = '';

            currentChannels.forEach(channel => {
                const channelCard = document.createElement('div');
                channelCard.className = 'channel-card';
                if (currentPlayingChannel === channel.id) {
                    channelCard.classList.add('playing');
                }
                if (channel.trusted) {
                    channelCard.classList.add('trusted');
                }

                channelCard.innerHTML = `
                    <div class="channel-logo">
                        ${channel.name}
                        <div class="bein-number">${channel.number}</div>
                        ${channel.trusted ? '<div class="trusted-badge"><i class="fas fa-check"></i></div>' : ''}
                    </div>
                    <div class="channel-info">
                        <h4>${channel.name} ${channel.trusted ? '<i class="fas fa-shield-alt" style="color: #4CAF50;"></i>' : ''}</h4>
                        <p>${channel.description}</p>
                        <div class="source-info">المصدر: ${channel.source}</div>
                        <div class="channel-badges">
                            <span class="status-live">مباشر</span>
                            <span class="quality-badge">HD</span>
                            ${channel.trusted ? '<span class="trusted-badge-text">✅ موثوق</span>' : '<span style="background: #ff9800; color: #fff; padding: 0.2rem 0.6rem; border-radius: 10px; font-size: 0.7rem;">🔄 تجريبي</span>'}
                        </div>
                    </div>
                `;

                channelCard.addEventListener('click', () => {
                    playChannel(channel);
                });

                channelsGrid.appendChild(channelCard);
            });
        }

        // تشغيل قناة
        async function playChannel(channel) {
            hideMessages();
            showInfo('جاري تحميل ' + channel.name + '...');

            document.getElementById('currentChannel').textContent = channel.name;
            document.getElementById('channelDescription').textContent = `${channel.description} - المصدر: ${channel.source}`;

            console.log('Playing channel:', channel.name, 'URL:', channel.url);

            try {
                const success = await tryPlayUrl(channel.url, channel);
                if (success) {
                    showSuccess(`✅ تم تحميل ${channel.name} بنجاح!`);
                    currentPlayingChannel = channel.id;
                    renderChannels();
                } else {
                    showError(`❌ فشل في تحميل ${channel.name} - جرب قناة أخرى`);
                }
            } catch (error) {
                console.error('Error playing channel:', error);
                showError(`❌ خطأ في تشغيل ${channel.name}: ${error.message}`);
            }
        }

        // تجربة تشغيل رابط
        function tryPlayUrl(url, channel) {
            return new Promise((resolve) => {
                const videoPlayer = document.getElementById('videoPlayer');

                if (hls) {
                    hls.destroy();
                }

                if (Hls.isSupported()) {
                    hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: false,
                        backBufferLength: 90,
                        maxBufferLength: 30,
                        debug: false
                    });

                    let resolved = false;

                    hls.loadSource(url);
                    hls.attachMedia(videoPlayer);

                    hls.on(Hls.Events.MANIFEST_PARSED, function() {
                        if (!resolved) {
                            resolved = true;
                            console.log('✅ Success for:', channel.name);
                            videoPlayer.play().catch(e => {
                                console.log('Autoplay prevented:', e);
                                showWarning('انقر على زر التشغيل لبدء المشاهدة');
                            });
                            resolve(true);
                        }
                    });

                    hls.on(Hls.Events.ERROR, function(event, data) {
                        if (!resolved && data.fatal) {
                            resolved = true;
                            console.error('❌ Failed for:', channel.name, data.details);
                            resolve(false);
                        }
                    });

                    // timeout بعد 10 ثواني
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            console.log('⏰ Timeout for:', channel.name);
                            resolve(false);
                        }
                    }, 10000);

                } else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
                    videoPlayer.src = url;
                    let resolved = false;

                    videoPlayer.addEventListener('loadedmetadata', function() {
                        if (!resolved) {
                            resolved = true;
                            resolve(true);
                        }
                    });

                    videoPlayer.addEventListener('error', function(e) {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    });

                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    }, 10000);
                } else {
                    resolve(false);
                }
            });
        }

        // فلترة القنوات
        function filterChannels(category) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (category === 'all') {
                currentChannels = [...trustedChannels];
            } else {
                currentChannels = trustedChannels.filter(channel => channel.category === category);
            }

            renderChannels();
        }

        // إعداد HLS
        function setupHLS() {
            if (!Hls.isSupported()) {
                console.log('HLS not supported, checking native support...');
            }
        }

        // إظهار رسائل
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 3000);
        }

        function showWarning(message) {
            hideMessages();
            const warningDiv = document.getElementById('warningMessage');
            warningDiv.textContent = message;
            warningDiv.style.display = 'block';
            setTimeout(() => warningDiv.style.display = 'none', 4000);
        }

        function showInfo(message) {
            hideMessages();
            const infoDiv = document.getElementById('infoMessage');
            infoDiv.textContent = message;
            infoDiv.style.display = 'block';
            setTimeout(() => infoDiv.style.display = 'none', 3000);
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('warningMessage').style.display = 'none';
            document.getElementById('infoMessage').style.display = 'none';
        }

        // تحميل عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
        });
    </script>
</body>
</html>
