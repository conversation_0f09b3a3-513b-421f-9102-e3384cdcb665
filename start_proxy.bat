@echo off
echo =======================================
echo    QLQ TV - Proxy Server للقنوات المشفرة
echo =======================================
echo.

REM Try different Python commands
echo Checking Python installation...

python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Found Python, starting server...
    echo Open your browser and go to: http://localhost:8080/proxy_channels.html
    echo Press Ctrl+C to stop the server
    echo.
    python proxy_server.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Found Python3, starting server...
    echo Open your browser and go to: http://localhost:8080/proxy_channels.html
    echo Press Ctrl+C to stop the server
    echo.
    python3 proxy_server.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Found py launcher, starting server...
    echo Open your browser and go to: http://localhost:8080/proxy_channels.html
    echo Press Ctrl+C to stop the server
    echo.
    py proxy_server.py
    goto :end
)

echo.
echo ERROR: Python not found in PATH!
echo.
echo Trying to find Python manually...
if exist "C:\Python*\python.exe" (
    for /d %%i in ("C:\Python*") do (
        echo Found Python at: %%i
        "%%i\python.exe" proxy_server.py
        goto :end
    )
)

if exist "%LOCALAPPDATA%\Programs\Python\Python*\python.exe" (
    for /d %%i in ("%LOCALAPPDATA%\Programs\Python\Python*") do (
        echo Found Python at: %%i
        "%%i\python.exe" proxy_server.py
        goto :end
    )
)

echo.
echo Python is installed but not found in PATH.
echo Please try one of these solutions:
echo.
echo 1. Add Python to PATH during installation
echo 2. Reinstall Python from https://python.org (check "Add to PATH")
echo 3. Or run manually: python proxy_server.py
echo.
pause

:end
