@echo off
echo =======================================
echo    QLQ TV - beIN Connect Integration
echo =======================================
echo.

echo Starting beIN Connect Proxy Server...
echo This will connect directly to beIN Connect and show all channels
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python...
    echo Starting beIN Connect Proxy Server on port 8080
    echo Open your browser and go to: http://localhost:8080/bein_connect_integration.html
    echo.
    echo Features:
    echo - Direct connection to beIN Connect
    echo - All beIN Sports channels (1-7)
    echo - beIN Sports English (1-3)
    echo - beIN Movies and Series
    echo - CORS bypass for seamless streaming
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    python bein_connect_proxy.py
    goto :end
)

REM Try Python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python3...
    echo Starting beIN Connect Proxy Server on port 8080
    echo Open your browser and go to: http://localhost:8080/bein_connect_integration.html
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    python3 bein_connect_proxy.py
    goto :end
)

REM Try py launcher
py --version >nul 2>&1
if %errorlevel_ == 0 (
    echo Using py launcher...
    echo Starting beIN Connect Proxy Server on port 8080
    echo Open your browser and go to: http://localhost:8080/bein_connect_integration.html
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    py bein_connect_proxy.py
    goto :end
)

echo.
echo ERROR: Python not found!
echo.
echo Please install Python from: https://www.python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation
echo.
echo Alternative: Open bein_connect_integration.html directly in your browser
echo (Note: Some features may not work without the proxy server)
echo.
pause

:end
