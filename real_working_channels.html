<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLQ TV - القنوات الحقيقية التي تعمل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            padding: 1rem 0;
            text-align: center;
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .logo span {
            font-size: 1.2rem;
            color: #ffd700;
        }

        .player-section {
            padding: 2rem 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
        }

        .player-container {
            background: #000000;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        #videoPlayer {
            width: 100%;
            height: 500px;
            background: #000000;
        }

        .player-info {
            padding: 1.5rem;
            background: #1a1a1a;
            text-align: center;
        }

        .channels-section {
            padding: 2rem 0;
        }

        .channels-section h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #ffffff;
        }

        .channels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .channel-card {
            background: #1a1a1a;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #333;
        }

        .channel-card:hover {
            transform: translateY(-5px);
            border-color: #ffd700;
        }

        .channel-card.playing {
            border-color: #4CAF50;
            background: #1a2a1a;
        }

        .channel-card.verified {
            border-color: #4CAF50;
        }

        .channel-logo {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #ffffff;
            font-weight: bold;
            position: relative;
        }

        .verified-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4CAF50;
            color: #ffffff;
            padding: 5px 8px;
            border-radius: 50%;
            font-size: 0.8rem;
        }

        .encrypted-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #ffd700;
            color: #000000;
            padding: 5px 8px;
            border-radius: 50%;
            font-size: 0.8rem;
        }

        .channel-info {
            padding: 1rem;
        }

        .channel-info h4 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .channel-info p {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .channel-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .status-live {
            background: #ff4444;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .quality-badge {
            background: #4CAF50;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .verified-badge-text {
            background: #4CAF50;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #ffd700;
            color: #000000;
            border-color: #ffd700;
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            display: none;
        }

        .error-message {
            background: #ff4444;
            color: #ffffff;
        }

        .success-message {
            background: #4CAF50;
            color: #ffffff;
        }

        .warning-message {
            background: #ff9800;
            color: #ffffff;
        }

        .info-message {
            background: #2196F3;
            color: #ffffff;
        }

        @media (max-width: 768px) {
            #videoPlayer {
                height: 250px;
            }
            
            .channels-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>QLQ TV</h1>
                <span>✅ القنوات الحقيقية التي تعمل 100%</span>
            </div>
        </div>
    </header>

    <!-- Video Player Section -->
    <section class="player-section">
        <div class="container">
            <div class="player-container">
                <video id="videoPlayer" controls crossorigin="anonymous">
                    متصفحك لا يدعم تشغيل الفيديو
                </video>
                <div class="player-info">
                    <h3 id="currentChannel">اختر قناة للمشاهدة</h3>
                    <p id="channelDescription">قنوات حقيقية مجربة ومضمونة</p>
                </div>
            </div>
            
            <div class="message error-message" id="errorMessage"></div>
            <div class="message success-message" id="successMessage"></div>
            <div class="message warning-message" id="warningMessage"></div>
            <div class="message info-message" id="infoMessage"></div>
        </div>
    </section>

    <!-- Channels Section -->
    <section class="channels-section">
        <div class="container">
            <h2>✅ القنوات المجربة والمضمونة</h2>
            
            <div class="filter-tabs">
                <button class="filter-btn active" onclick="filterChannels('all')">جميع القنوات</button>
                <button class="filter-btn" onclick="filterChannels('news')">الأخبار (مضمونة)</button>
                <button class="filter-btn" onclick="filterChannels('sports')">الرياضة</button>
                <button class="filter-btn" onclick="filterChannels('international')">دولية</button>
            </div>
            
            <div class="channels-grid" id="channelsGrid">
                <!-- سيتم تحميل القنوات هنا -->
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        // قائمة القنوات الحقيقية المجربة
        const realWorkingChannels = [
            // قنوات إخبارية مضمونة 100%
            {
                id: 'aljazeera',
                name: 'الجزيرة',
                category: 'news',
                url: 'https://live-hls-web-aje.getaj.net/AJE/01.m3u8',
                description: 'قناة الجزيرة الإخبارية - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'aljazeeramubasher',
                name: 'الجزيرة مباشر',
                category: 'news',
                url: 'https://live-hls-web-ajm.getaj.net/AJM/01.m3u8',
                description: 'الجزيرة مباشر - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'alarabiya',
                name: 'العربية',
                category: 'news',
                url: 'https://live.alarabiya.net/alarabiapublish/alarabiya.smil/playlist.m3u8',
                description: 'قناة العربية الإخبارية - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'skynewsarabia',
                name: 'سكاي نيوز عربية',
                category: 'news',
                url: 'https://stream.skynewsarabia.com/hls/sna.m3u8',
                description: 'سكاي نيوز عربية - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'france24ar',
                name: 'فرانس 24 عربي',
                category: 'international',
                url: 'https://static.france24.com/live/F24_AR_HI_HLS/live_web.m3u8',
                description: 'فرانس 24 باللغة العربية - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'dw',
                name: 'دويتشه فيله عربي',
                category: 'international',
                url: 'https://dwamdstream103.akamaized.net/hls/live/2015526/dwstream103/index.m3u8',
                description: 'دويتشه فيله باللغة العربية - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'rt_arabic',
                name: 'RT Arabic',
                category: 'international',
                url: 'https://rt-arb.rttv.com/live/rtarab/playlist.m3u8',
                description: 'روسيا اليوم عربي - مضمونة 100%',
                verified: true,
                working: true
            },
            {
                id: 'cnn_arabic',
                name: 'CNN عربية',
                category: 'international',
                url: 'https://cnn-cnninternational-1-eu.rakuten.wurl.tv/playlist.m3u8',
                description: 'CNN باللغة العربية',
                verified: false,
                working: true
            },
            // قنوات رياضية (تجريبية)
            {
                id: 'ssc_extra1',
                name: 'السعودية الرياضية',
                category: 'sports',
                url: 'https://edge.taghtia.com/sa/7.m3u8',
                description: 'السعودية الرياضية - تجريبي',
                verified: false,
                working: false
            },
            {
                id: 'dubai_sports',
                name: 'دبي الرياضية',
                category: 'sports',
                url: 'https://dmisxthvll.cdn.mangomolo.com/dubaisports/smil:dubaisports.smil/playlist.m3u8',
                description: 'دبي الرياضية - تجريبي',
                verified: false,
                working: false
            },
            // قنوات بديلة للبين سبورت (تجريبية)
            {
                id: 'bein_alt1',
                name: 'بديل بين سبورت 1',
                category: 'sports',
                url: 'https://siloh.pluto.tv/lilo/production/bein/master.m3u8',
                description: 'بديل تجريبي لبين سبورت',
                verified: false,
                working: false
            },
            {
                id: 'bein_alt2',
                name: 'بديل بين سبورت 2',
                category: 'sports',
                url: 'https://d2e1asnsl7br7b.cloudfront.net/7782e205e72f43aeb4a48ec97f66ebbe/index.m3u8',
                description: 'بديل تجريبي لبين سبورت',
                verified: false,
                working: false
            }
        ];

        let currentChannels = [...realWorkingChannels];
        let hls;
        let currentPlayingChannel = null;

        // تحميل القنوات
        function loadChannels() {
            renderChannels();
            setupHLS();
            showInfo('تم تحميل القنوات المجربة - القنوات المضمونة تعمل 100%');
        }

        // عرض القنوات
        function renderChannels() {
            const channelsGrid = document.getElementById('channelsGrid');
            channelsGrid.innerHTML = '';

            currentChannels.forEach(channel => {
                const channelCard = document.createElement('div');
                channelCard.className = 'channel-card';
                if (currentPlayingChannel === channel.id) {
                    channelCard.classList.add('playing');
                }
                if (channel.verified) {
                    channelCard.classList.add('verified');
                }
                
                channelCard.innerHTML = `
                    <div class="channel-logo">
                        ${channel.name}
                        ${channel.verified ? '<div class="verified-badge"><i class="fas fa-check"></i></div>' : ''}
                        ${!channel.working ? '<div class="encrypted-badge"><i class="fas fa-exclamation"></i></div>' : ''}
                    </div>
                    <div class="channel-info">
                        <h4>${channel.name} ${channel.verified ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i>' : ''}</h4>
                        <p>${channel.description}</p>
                        <div class="channel-badges">
                            <span class="status-live">مباشر</span>
                            <span class="quality-badge">HD</span>
                            ${channel.verified ? '<span class="verified-badge-text">✅ مضمونة</span>' : '<span style="background: #ff9800; color: #fff; padding: 0.2rem 0.6rem; border-radius: 10px; font-size: 0.7rem;">🔄 تجريبي</span>'}
                        </div>
                    </div>
                `;

                channelCard.addEventListener('click', () => {
                    playChannel(channel);
                });

                channelsGrid.appendChild(channelCard);
            });
        }

        // تشغيل قناة
        async function playChannel(channel) {
            hideMessages();
            
            if (!channel.working && !channel.verified) {
                showWarning(`${channel.name} قناة تجريبية - قد لا تعمل بشكل صحيح`);
            }
            
            showInfo('جاري تحميل ' + channel.name + '...');
            
            document.getElementById('currentChannel').textContent = channel.name;
            document.getElementById('channelDescription').textContent = channel.description;

            console.log('Playing channel:', channel.name, 'URL:', channel.url);

            try {
                const success = await tryPlayUrl(channel.url, channel);
                if (success) {
                    showSuccess(`✅ تم تحميل ${channel.name} بنجاح!`);
                    currentPlayingChannel = channel.id;
                    renderChannels();
                } else {
                    showError(`❌ فشل في تحميل ${channel.name} - جرب قناة أخرى`);
                }
            } catch (error) {
                console.error('Error playing channel:', error);
                showError(`❌ خطأ في تشغيل ${channel.name}: ${error.message}`);
            }
        }

        // تجربة تشغيل رابط
        function tryPlayUrl(url, channel) {
            return new Promise((resolve) => {
                const videoPlayer = document.getElementById('videoPlayer');

                if (hls) {
                    hls.destroy();
                }

                if (Hls.isSupported()) {
                    hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: false,
                        backBufferLength: 90,
                        maxBufferLength: 30,
                        debug: false
                    });
                    
                    let resolved = false;
                    
                    hls.loadSource(url);
                    hls.attachMedia(videoPlayer);
                    
                    hls.on(Hls.Events.MANIFEST_PARSED, function() {
                        if (!resolved) {
                            resolved = true;
                            console.log('✅ Manifest parsed successfully for:', channel.name);
                            videoPlayer.play().catch(e => {
                                console.log('Autoplay prevented:', e);
                                showWarning('انقر على زر التشغيل لبدء المشاهدة');
                            });
                            resolve(true);
                        }
                    });
                    
                    hls.on(Hls.Events.ERROR, function(event, data) {
                        if (!resolved && data.fatal) {
                            resolved = true;
                            console.error('❌ HLS Error for:', channel.name, data);
                            resolve(false);
                        }
                    });
                    
                    // timeout بعد 10 ثواني
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            console.log('⏰ Timeout for:', channel.name);
                            resolve(false);
                        }
                    }, 10000);
                    
                } else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
                    videoPlayer.src = url;
                    let resolved = false;
                    
                    videoPlayer.addEventListener('loadedmetadata', function() {
                        if (!resolved) {
                            resolved = true;
                            console.log('✅ Native HLS loaded for:', channel.name);
                            resolve(true);
                        }
                    });
                    
                    videoPlayer.addEventListener('error', function(e) {
                        if (!resolved) {
                            resolved = true;
                            console.error('❌ Native HLS error for:', channel.name, e);
                            resolve(false);
                        }
                    });
                    
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    }, 10000);
                } else {
                    console.error('❌ HLS not supported');
                    resolve(false);
                }
            });
        }

        // فلترة القنوات
        function filterChannels(category) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (category === 'all') {
                currentChannels = [...realWorkingChannels];
            } else {
                currentChannels = realWorkingChannels.filter(channel => channel.category === category);
            }

            renderChannels();
        }

        // إعداد HLS
        function setupHLS() {
            if (!Hls.isSupported()) {
                console.log('HLS not supported, checking native support...');
                const video = document.createElement('video');
                if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    console.log('✅ Native HLS support available');
                } else {
                    showError('❌ متصفحك لا يدعم تشغيل البث المباشر');
                }
            } else {
                console.log('✅ HLS.js supported');
            }
        }

        // إظهار رسائل
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 3000);
        }

        function showWarning(message) {
            hideMessages();
            const warningDiv = document.getElementById('warningMessage');
            warningDiv.textContent = message;
            warningDiv.style.display = 'block';
            setTimeout(() => warningDiv.style.display = 'none', 4000);
        }

        function showInfo(message) {
            hideMessages();
            const infoDiv = document.getElementById('infoMessage');
            infoDiv.textContent = message;
            infoDiv.style.display = 'block';
            setTimeout(() => infoDiv.style.display = 'none', 3000);
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('warningMessage').style.display = 'none';
            document.getElementById('infoMessage').style.display = 'none';
        }

        // تحميل عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
        });
    </script>
</body>
</html>
