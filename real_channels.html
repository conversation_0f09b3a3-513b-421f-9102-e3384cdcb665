<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLQ TV - القنوات المشفرة الحقيقية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            padding: 1rem 0;
            text-align: center;
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .logo span {
            font-size: 1.2rem;
            color: #ffd700;
        }

        .player-section {
            padding: 2rem 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
        }

        .player-container {
            background: #000000;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        #videoPlayer {
            width: 100%;
            height: 500px;
            background: #000000;
        }

        .player-info {
            padding: 1.5rem;
            background: #1a1a1a;
            text-align: center;
        }

        .channels-section {
            padding: 2rem 0;
        }

        .channels-section h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #ffffff;
        }

        .channels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .channel-card {
            background: #1a1a1a;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #333;
        }

        .channel-card:hover {
            transform: translateY(-5px);
            border-color: #ffd700;
        }

        .channel-logo {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #ffffff;
            font-weight: bold;
            position: relative;
        }

        .encrypted-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffd700;
            color: #000000;
            padding: 5px 8px;
            border-radius: 50%;
            font-size: 0.8rem;
        }

        .channel-info {
            padding: 1rem;
        }

        .channel-info h4 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .channel-info p {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .channel-url {
            color: #888;
            font-size: 0.8rem;
            word-break: break-all;
            background: #000;
            padding: 5px;
            border-radius: 5px;
            margin: 5px 0;
        }

        .channel-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .status-live {
            background: #ff4444;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .quality-badge {
            background: #4CAF50;
            color: #ffffff;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .encrypted-badge-text {
            background: #ffd700;
            color: #000000;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #ffd700;
            color: #000000;
            border-color: #ffd700;
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            display: none;
        }

        .error-message {
            background: #ff4444;
            color: #ffffff;
        }

        .success-message {
            background: #4CAF50;
            color: #ffffff;
        }

        .warning-message {
            background: #ff9800;
            color: #ffffff;
        }

        @media (max-width: 768px) {
            #videoPlayer {
                height: 250px;
            }
            
            .channels-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>QLQ TV</h1>
                <span>🔒 القنوات المشفرة الحقيقية</span>
            </div>
        </div>
    </header>

    <!-- Video Player Section -->
    <section class="player-section">
        <div class="container">
            <div class="player-container">
                <video id="videoPlayer" controls crossorigin="anonymous">
                    متصفحك لا يدعم تشغيل الفيديو
                </video>
                <div class="player-info">
                    <h3 id="currentChannel">اختر قناة مشفرة للمشاهدة</h3>
                    <p id="channelDescription">القنوات المشفرة الحقيقية من سيرفرات التطبيق</p>
                </div>
            </div>
            
            <div class="message error-message" id="errorMessage"></div>
            <div class="message success-message" id="successMessage"></div>
            <div class="message warning-message" id="warningMessage"></div>
        </div>
    </section>

    <!-- Channels Section -->
    <section class="channels-section">
        <div class="container">
            <h2>🔒 القنوات المشفرة الحقيقية</h2>
            
            <div class="filter-tabs">
                <button class="filter-btn active" onclick="filterChannels('all')">جميع القنوات</button>
                <button class="filter-btn" onclick="filterChannels('sports')">بين سبورت والرياضية</button>
                <button class="filter-btn" onclick="filterChannels('news')">الأخبار</button>
            </div>
            
            <div class="channels-grid" id="channelsGrid">
                <!-- سيتم تحميل القنوات هنا -->
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        // إعدادات Firebase من التطبيق
        const firebaseConfig = {
            apiKey: "AIzaSyCyIZ5yC-F_-Rcn0tnw560FU40oIiN00FY",
            databaseURL: "https://ostoraapp.firebaseio.com",
            projectId: "ostoraapp",
            storageBucket: "ostoraapp.appspot.com"
        };

        // قائمة القنوات المشفرة الحقيقية
        const realEncryptedChannels = [
            // بين سبورت - الروابط الحقيقية المشفرة
            {
                id: 'beinsports1',
                name: 'بي إن سبورت 1 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports1&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports1.json',
                description: 'بي إن سبورت 1 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsports2',
                name: 'بي إن سبورت 2 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports2&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports2.json',
                description: 'بي إن سبورت 2 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsports3',
                name: 'بي إن سبورت 3 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports3&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports3.json',
                description: 'بي إن سبورت 3 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsports4',
                name: 'بي إن سبورت 4 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports4&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports4.json',
                description: 'بي إن سبورت 4 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsports5',
                name: 'بي إن سبورت 5 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports5&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports5.json',
                description: 'بي إن سبورت 5 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsports6',
                name: 'بي إن سبورت 6 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports6&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports6.json',
                description: 'بي إن سبورت 6 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsports7',
                name: 'بي إن سبورت 7 HD',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsports7&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsports7.json',
                description: 'بي إن سبورت 7 - البث المباشر للمباريات',
                encrypted: true
            },
            {
                id: 'beinsportsen1',
                name: 'بي إن سبورت EN 1',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsportsen1&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsportsen1.json',
                description: 'بي إن سبورت الإنجليزية 1',
                encrypted: true
            },
            {
                id: 'beinsportsen2',
                name: 'بي إن سبورت EN 2',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=beinsportsen2&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/beinsportsen2.json',
                description: 'بي إن سبورت الإنجليزية 2',
                encrypted: true
            },
            // قنوات رياضية أخرى
            {
                id: 'ssc1',
                name: 'السعودية الرياضية 1',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=ssc1&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/ssc1.json',
                description: 'السعودية الرياضية 1',
                encrypted: true
            },
            {
                id: 'ssc2',
                name: 'السعودية الرياضية 2',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=ssc2&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/ssc2.json',
                description: 'السعودية الرياضية 2',
                encrypted: true
            },
            {
                id: 'adusports1',
                name: 'أبوظبي الرياضية 1',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=adusports1&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/adusports1.json',
                description: 'أبوظبي الرياضية 1',
                encrypted: true
            },
            {
                id: 'adusports2',
                name: 'أبوظبي الرياضية 2',
                category: 'sports',
                originalUrl: 'urlvplayer://redirect.m3u8?id=adusports2&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/adusports2.json',
                description: 'أبوظبي الرياضية 2',
                encrypted: true
            },
            // قنوات إخبارية
            {
                id: 'aljazeera',
                name: 'الجزيرة',
                category: 'news',
                originalUrl: 'urlvplayer://redirect.m3u8?id=aljazeera&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/aljazeera.json',
                description: 'قناة الجزيرة الإخبارية',
                encrypted: true
            },
            {
                id: 'aljazeeramubasher',
                name: 'الجزيرة مباشر',
                category: 'news',
                originalUrl: 'urlvplayer://redirect.m3u8?id=aljazeeramubasher&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/aljazeeramubasher.json',
                description: 'الجزيرة مباشر',
                encrypted: true
            },
            {
                id: 'alarabiya',
                name: 'العربية',
                category: 'news',
                originalUrl: 'urlvplayer://redirect.m3u8?id=alarabiya&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/alarabiya.json',
                description: 'قناة العربية الإخبارية',
                encrypted: true
            },
            {
                id: 'skynewsarabia',
                name: 'سكاي نيوز عربية',
                category: 'news',
                originalUrl: 'urlvplayer://redirect.m3u8?id=skynewsarabia&random_id=random_id&server=1',
                proxyUrl: 'https://ostoraapp.firebaseio.com/streams/skynewsarabia.json',
                description: 'سكاي نيوز عربية',
                encrypted: true
            }
        ];

        let currentChannels = [...realEncryptedChannels];
        let hls;

        // تحميل القنوات
        function loadChannels() {
            renderChannels();
            setupHLS();
            showWarning('هذه القنوات تستخدم الروابط الحقيقية من سيرفرات التطبيق');
        }

        // عرض القنوات
        function renderChannels() {
            const channelsGrid = document.getElementById('channelsGrid');
            channelsGrid.innerHTML = '';

            currentChannels.forEach(channel => {
                const channelCard = document.createElement('div');
                channelCard.className = 'channel-card';
                channelCard.innerHTML = `
                    <div class="channel-logo">
                        ${channel.name}
                        <div class="encrypted-badge"><i class="fas fa-lock"></i></div>
                    </div>
                    <div class="channel-info">
                        <h4>${channel.name} <i class="fas fa-lock" style="color: #ffd700;"></i></h4>
                        <p>${channel.description}</p>
                        <div class="channel-url">الرابط الأصلي: ${channel.originalUrl}</div>
                        <div class="channel-badges">
                            <span class="status-live">مباشر</span>
                            <span class="quality-badge">HD</span>
                            <span class="encrypted-badge-text">🔒 مشفرة</span>
                        </div>
                    </div>
                `;

                channelCard.addEventListener('click', () => {
                    playChannel(channel);
                });

                channelsGrid.appendChild(channelCard);
            });
        }

        // تشغيل قناة
        async function playChannel(channel) {
            hideMessages();
            showSuccess('جاري تحميل ' + channel.name + '...');
            
            document.getElementById('currentChannel').textContent = channel.name;
            document.getElementById('channelDescription').textContent = channel.description;

            console.log('Playing channel:', channel.name);
            console.log('Original URL:', channel.originalUrl);

            // معالجة الرابط المشفر
            let streamUrl = await processEncryptedUrl(channel);
            
            if (!streamUrl) {
                showError('فشل في معالجة رابط القناة المشفر');
                return;
            }

            console.log('Processed URL:', streamUrl);

            const videoPlayer = document.getElementById('videoPlayer');

            if (hls) {
                hls.destroy();
            }

            if (Hls.isSupported()) {
                hls = new Hls({
                    enableWorker: true,
                    lowLatencyMode: false,
                    backBufferLength: 90,
                    maxBufferLength: 30,
                    debug: false
                });
                
                hls.loadSource(streamUrl);
                hls.attachMedia(videoPlayer);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    console.log('Manifest parsed successfully');
                    hideMessages();
                    showSuccess('تم تحميل ' + channel.name + ' بنجاح!');
                    videoPlayer.play().catch(e => {
                        console.log('Autoplay prevented:', e);
                        showWarning('انقر على زر التشغيل لبدء المشاهدة');
                    });
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS Error:', data);
                    if (data.fatal) {
                        showError('خطأ في تشغيل ' + channel.name + ': ' + data.details);
                    }
                });
                
            } else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
                videoPlayer.src = streamUrl;
                videoPlayer.addEventListener('loadedmetadata', function() {
                    hideMessages();
                    showSuccess('تم تحميل ' + channel.name + ' بنجاح!');
                });
                videoPlayer.addEventListener('error', function(e) {
                    showError('خطأ في تشغيل ' + channel.name);
                });
            } else {
                showError('متصفحك لا يدعم تشغيل البث المباشر');
            }
        }

        // معالجة الرابط المشفر
        async function processEncryptedUrl(channel) {
            try {
                // إزالة urlvplayer:// prefix
                let url = channel.originalUrl.replace('urlvplayer://', '');
                
                // إضافة معاملات عشوائية
                const randomId = generateRandomId(40);
                url = url.replace('random_id', randomId);
                
                // إضافة timestamp
                url += '&_t=' + Date.now();
                
                // محاولة الحصول على الرابط الحقيقي من Firebase
                try {
                    const response = await fetch(channel.proxyUrl + '?auth=' + firebaseConfig.apiKey);
                    if (response.ok) {
                        const data = await response.json();
                        if (data && data.url) {
                            return data.url;
                        }
                    }
                } catch (e) {
                    console.log('Firebase fetch failed, using processed URL');
                }
                
                return url;
            } catch (error) {
                console.error('Error processing encrypted URL:', error);
                return null;
            }
        }

        // توليد ID عشوائي
        function generateRandomId(length = 40) {
            const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // فلترة القنوات
        function filterChannels(category) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (category === 'all') {
                currentChannels = [...realEncryptedChannels];
            } else {
                currentChannels = realEncryptedChannels.filter(channel => channel.category === category);
            }

            renderChannels();
        }

        // إعداد HLS
        function setupHLS() {
            if (!Hls.isSupported()) {
                console.log('HLS not supported, checking native support...');
            }
        }

        // إظهار رسائل
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 3000);
        }

        function showWarning(message) {
            hideMessages();
            const warningDiv = document.getElementById('warningMessage');
            warningDiv.textContent = message;
            warningDiv.style.display = 'block';
            setTimeout(() => warningDiv.style.display = 'none', 4000);
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('warningMessage').style.display = 'none';
        }

        // تحميل عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
        });
    </script>
</body>
</html>
