#!/usr/bin/env python3
"""
QLQ TV Proxy Server
يحل مشكلة CORS ويعالج الروابط المشفرة
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import json
import random
import string
import time
from urllib.error import URLError, HTTPError

class QLQTVProxyHandler(http.server.SimpleHTTPRequestHandler):
    
    def do_GET(self):
        # إذا كان الطلب للـ proxy
        if self.path.startswith('/proxy/'):
            self.handle_proxy_request()
        # إذا كان الطلب لمعالجة رابط مشفر
        elif self.path.startswith('/decrypt/'):
            self.handle_decrypt_request()
        # طلبات الملفات العادية
        else:
            super().do_GET()
    
    def handle_proxy_request(self):
        """معالجة طلبات الـ proxy"""
        try:
            # استخراج الرابط من المسار
            encoded_url = self.path[7:]  # إزالة /proxy/
            target_url = urllib.parse.unquote(encoded_url)
            
            print(f"Proxying request to: {target_url}")
            
            # إنشاء الطلب
            req = urllib.request.Request(target_url)
            req.add_header('User-Agent', 'QLQ TV/1.0')
            req.add_header('Accept', '*/*')
            
            # تنفيذ الطلب
            with urllib.request.urlopen(req, timeout=10) as response:
                content = response.read()
                content_type = response.headers.get('Content-Type', 'application/octet-stream')
                
                # إرسال الاستجابة
                self.send_response(200)
                self.send_header('Content-Type', content_type)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', '*')
                self.end_headers()
                self.wfile.write(content)
                
        except Exception as e:
            print(f"Proxy error: {e}")
            self.send_error(500, f"Proxy error: {str(e)}")
    
    def handle_decrypt_request(self):
        """معالجة طلبات فك التشفير"""
        try:
            # استخراج معرف القناة من المسار
            channel_id = self.path[9:]  # إزالة /decrypt/
            
            print(f"Decrypting channel: {channel_id}")
            
            # معالجة الرابط المشفر
            decrypted_url = self.decrypt_channel_url(channel_id)
            
            if decrypted_url:
                # إرسال الرابط المفكوك
                response_data = {
                    'success': True,
                    'url': decrypted_url,
                    'channel_id': channel_id,
                    'timestamp': int(time.time())
                }
            else:
                response_data = {
                    'success': False,
                    'error': 'Failed to decrypt channel URL',
                    'channel_id': channel_id
                }
            
            # إرسال الاستجابة JSON
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            print(f"Decrypt error: {e}")
            self.send_error(500, f"Decrypt error: {str(e)}")
    
    def decrypt_channel_url(self, channel_id):
        """فك تشفير رابط القناة"""
        try:
            # قاعدة بيانات القنوات المشفرة
            encrypted_channels = {
                'beinsports1': 'redirect.m3u8?id=beinsports1&random_id={}&server=1',
                'beinsports2': 'redirect.m3u8?id=beinsports2&random_id={}&server=1',
                'beinsports3': 'redirect.m3u8?id=beinsports3&random_id={}&server=1',
                'beinsports4': 'redirect.m3u8?id=beinsports4&random_id={}&server=1',
                'beinsports5': 'redirect.m3u8?id=beinsports5&random_id={}&server=1',
                'beinsports6': 'redirect.m3u8?id=beinsports6&random_id={}&server=1',
                'beinsports7': 'redirect.m3u8?id=beinsports7&random_id={}&server=1',
                'beinsportsen1': 'redirect.m3u8?id=beinsportsen1&random_id={}&server=1',
                'beinsportsen2': 'redirect.m3u8?id=beinsportsen2&random_id={}&server=1',
                'ssc1': 'redirect.m3u8?id=ssc1&random_id={}&server=1',
                'ssc2': 'redirect.m3u8?id=ssc2&random_id={}&server=1',
                'adusports1': 'redirect.m3u8?id=adusports1&random_id={}&server=1',
                'adusports2': 'redirect.m3u8?id=adusports2&random_id={}&server=1',
                'aljazeera': 'redirect.m3u8?id=aljazeera&random_id={}&server=1',
                'aljazeeramubasher': 'redirect.m3u8?id=aljazeeramubasher&random_id={}&server=1',
                'alarabiya': 'redirect.m3u8?id=alarabiya&random_id={}&server=1',
                'skynewsarabia': 'redirect.m3u8?id=skynewsarabia&random_id={}&server=1'
            }
            
            if channel_id in encrypted_channels:
                # توليد random_id
                random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=40))
                
                # بناء الرابط
                url_template = encrypted_channels[channel_id]
                decrypted_url = url_template.format(random_id)
                
                # إضافة timestamp
                decrypted_url += f'&_t={int(time.time())}'
                
                print(f"Decrypted URL for {channel_id}: {decrypted_url}")
                return decrypted_url
            
            return None
            
        except Exception as e:
            print(f"Error decrypting {channel_id}: {e}")
            return None
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()

def run_server(port=8080):
    """تشغيل الخادم"""
    try:
        with socketserver.TCPServer(("", port), QLQTVProxyHandler) as httpd:
            print("=" * 50)
            print("    QLQ TV Proxy Server")
            print("=" * 50)
            print(f"Server running on port {port}")
            print(f"Open your browser and go to: http://localhost:{port}/proxy_channels.html")
            print("Press Ctrl+C to stop the server")
            print("=" * 50)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")

if __name__ == "__main__":
    run_server()
