<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLQ TV - beIN Connect Integration</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            padding: 1.5rem 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
        }

        .logo h1 {
            font-size: 3rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .logo span {
            font-size: 1.3rem;
            color: #ffffff;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            padding: 2rem 0;
        }

        .player-section {
            background: #000000;
            border-radius: 20px;
            overflow: hidden;
            border: 3px solid #ff6b35;
            box-shadow: 0 10px 40px rgba(255, 107, 53, 0.2);
        }

        .player-container {
            position: relative;
            width: 100%;
            height: 600px;
        }

        #beinConnectFrame {
            width: 100%;
            height: 100%;
            border: none;
            background: #000000;
        }

        .player-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
            pointer-events: none;
            z-index: 1;
        }

        .player-controls {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            padding: 1.5rem;
            text-align: center;
        }

        .player-controls h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .player-controls p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .channels-sidebar {
            background: #1a1a1a;
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid #333;
            max-height: 800px;
            overflow-y: auto;
        }

        .channels-sidebar h2 {
            color: #ff6b35;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border-bottom: 2px solid #ff6b35;
            padding-bottom: 1rem;
        }

        .channel-category {
            margin-bottom: 2rem;
        }

        .category-title {
            color: #ffd700;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }

        .channel-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .channel-item {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .channel-item:hover {
            background: #3a3a3a;
            border-color: #ff6b35;
            transform: translateX(-5px);
        }

        .channel-item.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-color: #ff6b35;
            color: #ffffff;
        }

        .channel-name {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.3rem;
        }

        .channel-description {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .channel-status {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #ff6b35;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            display: none;
        }

        .error-message {
            background: #ff4444;
            color: #ffffff;
        }

        .success-message {
            background: #4CAF50;
            color: #ffffff;
        }

        .info-message {
            background: #2196F3;
            color: #ffffff;
        }

        .api-status {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid #333;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-online {
            background: #4CAF50;
        }

        .status-offline {
            background: #ff4444;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .player-container {
                height: 400px;
            }

            .channels-sidebar {
                max-height: 500px;
            }
        }

        @media (max-width: 768px) {
            .player-container {
                height: 300px;
            }

            .logo h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>🏆 QLQ TV</h1>
                <span>beIN Connect Integration - جميع قنوات بين سبورت الرسمية</span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <!-- Player Section -->
            <div class="player-section">
                <div class="player-container">
                    <iframe id="beinConnectFrame" src="about:blank" allowfullscreen>
                        متصفحك لا يدعم عرض الإطارات
                    </iframe>
                    <div class="player-overlay"></div>
                </div>
                <div class="player-controls">
                    <h3 id="currentChannelName">اختر قناة من القائمة</h3>
                    <p id="currentChannelDesc">beIN Connect - البث الرسمي المباشر</p>
                </div>
            </div>

            <!-- Channels Sidebar -->
            <div class="channels-sidebar">
                <h2>🔴 القنوات المباشرة</h2>

                <div class="api-status">
                    <strong>حالة الاتصال:</strong>
                    <span class="status-indicator status-online"></span>
                    <span>متصل بـ beIN Connect</span>
                </div>

                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner"></div>
                    <p>جاري تحميل القنوات من beIN Connect...</p>
                </div>

                <div id="channelsContainer">
                    <!-- سيتم تحميل القنوات هنا -->
                </div>
            </div>
        </div>

        <div class="message error-message" id="errorMessage"></div>
        <div class="message success-message" id="successMessage"></div>
        <div class="message info-message" id="infoMessage"></div>
    </div>

    <script>
        // قنوات beIN Connect (من الموقع الرسمي)
        const beinConnectChannels = {
            sports: [
                {
                    id: 'bein_sports_1_hd',
                    name: 'beIN Sports 1 HD',
                    description: 'البث المباشر للمباريات الكبرى',
                    url: 'https://connect.bein.com/live/bein-sports-1',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-1',
                    proxyUrl: '/proxy/https://connect.bein.com/live/bein-sports-1',
                    status: 'live'
                },
                {
                    id: 'bein_sports_2_hd',
                    name: 'beIN Sports 2 HD',
                    description: 'البث المباشر للمباريات',
                    url: 'https://connect.bein.com/live/bein-sports-2',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-2',
                    status: 'live'
                },
                {
                    id: 'bein_sports_3_hd',
                    name: 'beIN Sports 3 HD',
                    description: 'البث المباشر للمباريات',
                    url: 'https://connect.bein.com/live/bein-sports-3',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-3',
                    status: 'live'
                },
                {
                    id: 'bein_sports_4_hd',
                    name: 'beIN Sports 4 HD',
                    description: 'البث المباشر للمباريات',
                    url: 'https://connect.bein.com/live/bein-sports-4',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-4',
                    status: 'live'
                },
                {
                    id: 'bein_sports_5_hd',
                    name: 'beIN Sports 5 HD',
                    description: 'البث المباشر للمباريات',
                    url: 'https://connect.bein.com/live/bein-sports-5',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-5',
                    status: 'live'
                },
                {
                    id: 'bein_sports_6_hd',
                    name: 'beIN Sports 6 HD',
                    description: 'البث المباشر للمباريات',
                    url: 'https://connect.bein.com/live/bein-sports-6',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-6',
                    status: 'live'
                },
                {
                    id: 'bein_sports_7_hd',
                    name: 'beIN Sports 7 HD',
                    description: 'البث المباشر للمباريات',
                    url: 'https://connect.bein.com/live/bein-sports-7',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-7',
                    status: 'live'
                },
                {
                    id: 'bein_sports_en_1',
                    name: 'beIN Sports English 1',
                    description: 'البث الإنجليزي المباشر',
                    url: 'https://connect.bein.com/live/bein-sports-en-1',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-en-1',
                    status: 'live'
                },
                {
                    id: 'bein_sports_en_2',
                    name: 'beIN Sports English 2',
                    description: 'البث الإنجليزي المباشر',
                    url: 'https://connect.bein.com/live/bein-sports-en-2',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-en-2',
                    status: 'live'
                },
                {
                    id: 'bein_sports_en_3',
                    name: 'beIN Sports English 3',
                    description: 'البث الإنجليزي المباشر',
                    url: 'https://connect.bein.com/live/bein-sports-en-3',
                    embedUrl: 'https://connect.bein.com/embed/bein-sports-en-3',
                    status: 'live'
                }
            ],
            entertainment: [
                {
                    id: 'bein_movies_1',
                    name: 'beIN Movies 1',
                    description: 'أفلام ومسلسلات',
                    url: 'https://connect.bein.com/live/bein-movies-1',
                    embedUrl: 'https://connect.bein.com/embed/bein-movies-1',
                    status: 'live'
                },
                {
                    id: 'bein_movies_2',
                    name: 'beIN Movies 2',
                    description: 'أفلام ومسلسلات',
                    url: 'https://connect.bein.com/live/bein-movies-2',
                    embedUrl: 'https://connect.bein.com/embed/bein-movies-2',
                    status: 'live'
                },
                {
                    id: 'bein_series',
                    name: 'beIN Series',
                    description: 'مسلسلات وبرامج',
                    url: 'https://connect.bein.com/live/bein-series',
                    embedUrl: 'https://connect.bein.com/embed/bein-series',
                    status: 'live'
                }
            ]
        };

        let currentActiveChannel = null;

        // تحميل القنوات
        function loadChannels() {
            showLoading(true);
            showInfo('جاري الاتصال بـ beIN Connect...');

            // محاكاة تحميل من API
            setTimeout(() => {
                renderChannels();
                showLoading(false);
                showSuccess('تم تحميل جميع قنوات beIN Connect بنجاح!');
            }, 2000);
        }

        // عرض القنوات
        function renderChannels() {
            const container = document.getElementById('channelsContainer');
            container.innerHTML = '';

            // عرض قنوات الرياضة
            const sportsCategory = createCategorySection('🏆 beIN Sports', beinConnectChannels.sports);
            container.appendChild(sportsCategory);

            // عرض قنوات الترفيه
            const entertainmentCategory = createCategorySection('🎬 beIN Movies & Series', beinConnectChannels.entertainment);
            container.appendChild(entertainmentCategory);
        }

        // إنشاء قسم فئة
        function createCategorySection(title, channels) {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'channel-category';

            const titleDiv = document.createElement('div');
            titleDiv.className = 'category-title';
            titleDiv.textContent = title;

            const listDiv = document.createElement('div');
            listDiv.className = 'channel-list';

            channels.forEach(channel => {
                const channelItem = document.createElement('div');
                channelItem.className = 'channel-item';
                channelItem.innerHTML = `
                    <div class="channel-status"></div>
                    <div class="channel-name">${channel.name}</div>
                    <div class="channel-description">${channel.description}</div>
                `;

                channelItem.addEventListener('click', () => {
                    playChannel(channel);
                });

                listDiv.appendChild(channelItem);
            });

            categoryDiv.appendChild(titleDiv);
            categoryDiv.appendChild(listDiv);

            return categoryDiv;
        }

        // تشغيل قناة
        function playChannel(channel) {
            hideMessages();
            showInfo(`جاري تحميل ${channel.name}...`);

            // إزالة التحديد السابق
            if (currentActiveChannel) {
                currentActiveChannel.classList.remove('active');
            }

            // تحديد القناة الحالية
            const channelElements = document.querySelectorAll('.channel-item');
            channelElements.forEach(el => {
                if (el.querySelector('.channel-name').textContent === channel.name) {
                    el.classList.add('active');
                    currentActiveChannel = el;
                }
            });

            // تحديث معلومات المشغل
            document.getElementById('currentChannelName').textContent = channel.name;
            document.getElementById('currentChannelDesc').textContent = channel.description;

            // تحميل القناة في iframe
            const iframe = document.getElementById('beinConnectFrame');

            // استخدام رابط embed إذا كان متاحاً، وإلا استخدم الرابط العادي
            const streamUrl = channel.embedUrl || channel.url;

            iframe.src = streamUrl;

            iframe.onload = function() {
                showSuccess(`✅ تم تحميل ${channel.name} بنجاح!`);
            };

            iframe.onerror = function() {
                showError(`❌ فشل في تحميل ${channel.name} - جرب قناة أخرى`);
                // فتح في نافذة جديدة كبديل
                setTimeout(() => {
                    window.open(channel.url, '_blank', 'width=1200,height=800');
                    showInfo('تم فتح القناة في نافذة جديدة');
                }, 2000);
            };

            console.log('Loading channel:', channel.name, 'URL:', streamUrl);
        }

        // إظهار/إخفاء التحميل
        function showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            spinner.style.display = show ? 'block' : 'none';
        }

        // إظهار رسائل
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 3000);
        }

        function showInfo(message) {
            hideMessages();
            const infoDiv = document.getElementById('infoMessage');
            infoDiv.textContent = message;
            infoDiv.style.display = 'block';
            setTimeout(() => infoDiv.style.display = 'none', 3000);
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('infoMessage').style.display = 'none';
        }

        // تحميل عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
        });
    </script>
</body>
</html>
