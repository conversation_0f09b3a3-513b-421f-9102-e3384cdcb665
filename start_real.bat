@echo off
echo =======================================
echo    QLQ TV - القنوات المشفرة الحقيقية
echo =======================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting real encrypted channels with Python...
    echo Open your browser and go to: http://localhost:8080/real_channels.html
    echo Press Ctrl+C to stop the server
    echo.
    python -m http.server 8080
) else (
    REM Check if Node.js is installed
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo Starting real encrypted channels with Node.js...
        echo Installing dependencies...
        npm install http-server -g
        echo Open your browser and go to: http://localhost:8080/real_channels.html
        echo Press Ctrl+C to stop the server
        echo.
        npx http-server -p 8080 -c-1
    ) else (
        echo.
        echo ERROR: Neither Python nor Node.js found!
        echo.
        echo Please install one of the following:
        echo 1. Python 3.x from https://python.org
        echo 2. Node.js from https://nodejs.org
        echo.
        echo Then run this script again.
        echo.
        pause
    )
)
