@echo off
echo =======================================
echo    QLQ TV - بين سبورت النهائية
echo =======================================
echo.

echo Starting beIN Sports Ultimate Solution...
echo.

REM Try Python first
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python web server...
    echo Open your browser and go to: http://localhost:8080/beinsports_ultimate.html
    echo Press Ctrl+C to stop the server
    echo.
    python -m http.server 8080
    goto :end
)

REM Try Python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python3 web server...
    echo Open your browser and go to: http://localhost:8080/beinsports_ultimate.html
    echo Press Ctrl+C to stop the server
    echo.
    python3 -m http.server 8080
    goto :end
)

REM Try py launcher
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using py launcher web server...
    echo Open your browser and go to: http://localhost:8080/beinsports_ultimate.html
    echo Press Ctrl+C to stop the server
    echo.
    py -m http.server 8080
    goto :end
)

REM Try Node.js
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Node.js web server...
    echo Installing http-server...
    npm install -g http-server
    echo Open your browser and go to: http://localhost:8080/beinsports_ultimate.html
    echo Press Ctrl+C to stop the server
    echo.
    npx http-server -p 8080 -c-1
    goto :end
)

echo.
echo No web server found!
echo.
echo Opening file directly in default browser...
echo Note: Some features may not work due to CORS restrictions
echo.
start beinsports_ultimate.html

:end
